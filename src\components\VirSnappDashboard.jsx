import React, { useState, useContext, useEffect } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import ChannelListManager from './ChannelListManager';
import ChannelListView from './ChannelListView';
import ChannelDiscovery from './ChannelDiscovery';
import AuthModal from './AuthModal';
import DebugInfo from './DebugInfo';
import { AiOutlineUser, AiOutlineLogout, AiOutlineSearch, AiOutlineFolder } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';

const VirSnappDashboard = () => {
    const [activeTab, setActiveTab] = useState('lists'); // 'lists', 'discover'
    const [selectedList, setSelectedList] = useState(null);
    const [showAuthModal, setShowAuthModal] = useState(false);
    const [authMode, setAuthMode] = useState('signin');

    const { 
        channelLists, 
        loading, 
        error, 
        clearError,
        addChannelToList 
    } = useContext(Context);
    
    const { user, signOut, loading: authLoading } = useAuth();

    useEffect(() => {
        document.getElementById("root").classList.remove("custom-h");
    }, []);

    const handleSignOut = async () => {
        try {
            await signOut();
            setSelectedList(null);
        } catch (error) {
            console.error('Sign out error:', error);
        }
    };

    const handleAuthClick = (mode) => {
        setAuthMode(mode);
        setShowAuthModal(true);
    };

    const handleListSelect = (list) => {
        setSelectedList(list);
        setActiveTab('lists');
    };

    const handleAddChannelsToList = async (listId, channels) => {
        try {
            for (const channel of channels) {
                await addChannelToList(listId, channel);
            }
            // Switch to the list view to show the added channels
            const targetList = channelLists.find(list => list.id === listId);
            if (targetList) {
                setSelectedList(targetList);
                setActiveTab('lists');
            }
        } catch (error) {
            console.error('Error adding channels to list:', error);
        }
    };

    return (
        <div className="flex flex-row h-[calc(100%-56px)] bg-gray-100 dark:bg-gray-900">
            {/* Sidebar */}
            <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
                {/* User Section */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    {user ? (
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                    <AiOutlineUser className="text-white" size={20} />
                                </div>
                                <div>
                                    <p className="font-medium text-gray-900 dark:text-white truncate">
                                        {user.email}
                                    </p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        VirSnapp User
                                    </p>
                                </div>
                            </div>
                            <button
                                onClick={handleSignOut}
                                disabled={authLoading}
                                className="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 p-2"
                                title="Sign out"
                            >
                                {authLoading ? (
                                    <BiLoaderAlt className="animate-spin" size={18} />
                                ) : (
                                    <AiOutlineLogout size={18} />
                                )}
                            </button>
                        </div>
                    ) : (
                        <div className="space-y-2">
                            <p className="text-gray-600 dark:text-gray-400 text-sm">
                                Sign in to manage your channel lists
                            </p>
                            <div className="flex gap-2">
                                <button
                                    onClick={() => handleAuthClick('signin')}
                                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                >
                                    Sign In
                                </button>
                                <button
                                    onClick={() => handleAuthClick('signup')}
                                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
                                >
                                    Sign Up
                                </button>
                            </div>
                        </div>
                    )}
                </div>

                {/* Navigation Tabs */}
                <div className="flex border-b border-gray-200 dark:border-gray-700">
                    <button
                        onClick={() => setActiveTab('lists')}
                        className={`flex-1 px-4 py-3 text-sm font-medium transition-colors flex items-center justify-center gap-2 ${
                            activeTab === 'lists'
                                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                        }`}
                    >
                        <AiOutlineFolder size={16} />
                        My Lists
                    </button>
                    <button
                        onClick={() => setActiveTab('discover')}
                        className={`flex-1 px-4 py-3 text-sm font-medium transition-colors flex items-center justify-center gap-2 ${
                            activeTab === 'discover'
                                ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                        }`}
                    >
                        <AiOutlineSearch size={16} />
                        Discover
                    </button>
                </div>

                {/* Sidebar Content */}
                <div className="flex-1 overflow-y-auto">
                    {activeTab === 'lists' && (
                        <div className="p-4">
                            <ChannelListManager
                                onListSelect={handleListSelect}
                                selectedListId={selectedList?.id}
                            />
                        </div>
                    )}
                    
                    {activeTab === 'discover' && (
                        <div className="p-4">
                            <div className="text-center mb-4">
                                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Channel Discovery
                                </h3>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Search and add channels to your lists
                                </p>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 overflow-y-auto">
                {error && (
                    <div className="m-4 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md text-red-700 dark:text-red-400 flex justify-between items-center">
                        <span>{error}</span>
                        <button
                            onClick={clearError}
                            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200"
                        >
                            ×
                        </button>
                    </div>
                )}

                <div className="p-6">
                    {activeTab === 'lists' && (
                        <ChannelListView selectedList={selectedList} />
                    )}
                    
                    {activeTab === 'discover' && (
                        <ChannelDiscovery onAddToList={handleAddChannelsToList} />
                    )}
                </div>
            </div>

            {/* Auth Modal */}
            <AuthModal
                isOpen={showAuthModal}
                onClose={() => setShowAuthModal(false)}
                initialMode={authMode}
            />

            {/* Debug Info (Development Only) */}
            <DebugInfo />
        </div>
    );
};

export default VirSnappDashboard;
