import React, { useContext } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

const DebugInfo = () => {
    const { user, loading: authLoading, error: authError } = useAuth();
    const { error: contextError, loading: contextLoading } = useContext(Context);

    const checkSupabaseConnection = async () => {
        try {
            const { data, error } = await supabase.from('channel_lists').select('count', { count: 'exact', head: true });
            console.log('Supabase connection test:', { data, error });
            alert(`Supabase connection: ${error ? 'Failed - ' + error.message : 'Success'}`);
        } catch (error) {
            console.error('Supabase connection error:', error);
            alert(`Supabase connection error: ${error.message}`);
        }
    };

    const checkEnvironmentVariables = () => {
        const envVars = {
            REACT_APP_SUPABASE_URL: process.env.REACT_APP_SUPABASE_URL,
            REACT_APP_SUPABASE_ANON_KEY: process.env.REACT_APP_SUPABASE_ANON_KEY ? 'Set' : 'Not Set',
            REACT_APP_YOUTUBE_API_KEY: process.env.REACT_APP_YOUTUBE_API_KEY ? 'Set' : 'Not Set'
        };
        console.log('Environment Variables:', envVars);
        alert(`Environment Variables:\n${JSON.stringify(envVars, null, 2)}`);
    };

    if (process.env.NODE_ENV !== 'development') {
        return null; // Only show in development
    }

    return (
        <div className="fixed bottom-4 left-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
            <h3 className="font-bold mb-2">Debug Info</h3>
            
            <div className="space-y-2 text-sm">
                <div>
                    <strong>User:</strong> {user ? user.email : 'Not authenticated'}
                </div>
                <div>
                    <strong>Auth Loading:</strong> {authLoading ? 'Yes' : 'No'}
                </div>
                <div>
                    <strong>Context Loading:</strong> {contextLoading ? 'Yes' : 'No'}
                </div>
                <div>
                    <strong>Auth Error:</strong> {authError || 'None'}
                </div>
                <div>
                    <strong>Context Error:</strong> {contextError || 'None'}
                </div>
            </div>

            <div className="mt-4 space-y-2">
                <button
                    onClick={checkEnvironmentVariables}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs"
                >
                    Check Env Vars
                </button>
                <button
                    onClick={checkSupabaseConnection}
                    className="w-full bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs"
                >
                    Test Supabase
                </button>
            </div>
        </div>
    );
};

export default DebugInfo;
