import React, { useState, useContext } from 'react';
import { Context } from '../context/contextApi';
import { AiOutlineDelete, AiOutlineEye, AiOutlineCalendar } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';
import { BsFillCheckCircleFill } from 'react-icons/bs';

const ChannelCard = ({ channel, onRemove, showRemoveButton = true }) => {
    const [loading, setLoading] = useState(false);
    const { removeChannelFromList } = useContext(Context);

    const handleRemove = async () => {
        if (!window.confirm(`Remove "${channel.channel_name}" from this list?`)) {
            return;
        }

        try {
            setLoading(true);
            await removeChannelFromList(channel.id);
            
            if (onRemove) {
                onRemove(channel);
            }
        } catch (error) {
            console.error('Error removing channel:', error);
        } finally {
            setLoading(false);
        }
    };

    const formatSubscriberCount = (count) => {
        if (!count) return 'Unknown';
        if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
        if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
        return count.toString();
    };

    const formatDate = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const openChannelInNewTab = () => {
        if (channel.channel_url) {
            window.open(channel.channel_url, '_blank', 'noopener,noreferrer');
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 hover:shadow-md transition-shadow">
            <div className="flex items-start gap-4">
                {/* Channel Thumbnail */}
                <div className="flex-shrink-0">
                    {channel.channel_thumbnail ? (
                        <img
                            src={channel.channel_thumbnail}
                            alt={channel.channel_name}
                            className="w-16 h-16 rounded-full object-cover"
                            onError={(e) => {
                                e.target.style.display = 'none';
                            }}
                        />
                    ) : (
                        <div className="w-16 h-16 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                            <span className="text-gray-500 dark:text-gray-400 text-xl font-bold">
                                {channel.channel_name?.charAt(0)?.toUpperCase() || '?'}
                            </span>
                        </div>
                    )}
                </div>

                {/* Channel Info */}
                <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 dark:text-white text-lg truncate flex items-center gap-2">
                                {channel.channel_name}
                                {/* Verified badge placeholder - would need to be determined from API */}
                                {channel.is_verified && (
                                    <BsFillCheckCircleFill className="text-blue-500 text-sm flex-shrink-0" />
                                )}
                            </h3>
                            
                            {/* Channel Stats */}
                            <div className="flex flex-wrap gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                <div className="flex items-center gap-1">
                                    <span>{formatSubscriberCount(channel.subscriber_count)} subscribers</span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <span>{channel.video_count || 0} videos</span>
                                </div>
                            </div>

                            {/* Added Date */}
                            {channel.added_at && (
                                <div className="flex items-center gap-1 mt-2 text-xs text-gray-500 dark:text-gray-400">
                                    <AiOutlineCalendar size={12} />
                                    <span>Added {formatDate(channel.added_at)}</span>
                                </div>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-2 ml-4">
                            <button
                                onClick={openChannelInNewTab}
                                className="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                                title="View channel on YouTube"
                            >
                                <AiOutlineEye size={18} />
                            </button>
                            
                            {showRemoveButton && (
                                <button
                                    onClick={handleRemove}
                                    disabled={loading}
                                    className="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
                                    title="Remove from list"
                                >
                                    {loading ? (
                                        <BiLoaderAlt className="animate-spin" size={18} />
                                    ) : (
                                        <AiOutlineDelete size={18} />
                                    )}
                                </button>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* Channel URL (for reference) */}
            {channel.channel_url && (
                <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <a
                        href={channel.channel_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 dark:text-blue-400 hover:underline truncate block"
                    >
                        {channel.channel_url}
                    </a>
                </div>
            )}
        </div>
    );
};

export default ChannelCard;
