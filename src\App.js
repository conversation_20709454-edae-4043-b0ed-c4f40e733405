import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";

import Header from "./components/Header";
import Feed from "./components/Feed";
import SearchResult from "./components/SearchResult";
import VideoDetails from "./components/VideoDetails";
import VirSnappDashboard from "./components/VirSnappDashboard";
import ErrorBoundary from "./components/ErrorBoundary";
import { ToastProvider } from "./components/Toast";
import { AppContext } from "./context/contextApi";
import { AuthProvider } from "./context/AuthContext";

const App = () => {
    return (
        <ErrorBoundary>
            <ToastProvider>
                <AuthProvider>
                    <AppContext>
                        <BrowserRouter>
                            <div className="flex flex-col h-full">
                                <Header />
                                <Routes>
                                    <Route path="/" exact element={<VirSnappDashboard />} />
                                    <Route path="/youtube" element={<Feed />} />
                                    <Route
                                        path="/searchResult/:searchQuery"
                                        element={<SearchResult />}
                                    />
                                    <Route path="/video/:id" element={<VideoDetails />} />
                                </Routes>
                            </div>
                        </BrowserRouter>
                    </AppContext>
                </AuthProvider>
            </ToastProvider>
        </ErrorBoundary>
    );
};

export default App;
