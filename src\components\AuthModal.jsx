import React, { useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { isValidEmail, validatePassword } from '../utils/validation';
import { AiOutlineClose, AiOutlineEye, AiOutlineEyeInvisible } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';

const AuthModal = ({ isOpen, onClose, initialMode = 'signin' }) => {
    const [mode, setMode] = useState(initialMode); // 'signin', 'signup', 'reset'
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [localError, setLocalError] = useState('');
    const [success, setSuccess] = useState('');

    const { signIn, signUp, resetPassword, loading, error, clearError } = useAuth();

    const resetForm = () => {
        setEmail('');
        setPassword('');
        setConfirmPassword('');
        setLocalError('');
        setSuccess('');
        setShowPassword(false);
        setShowConfirmPassword(false);
        clearError();
    };

    const handleClose = () => {
        resetForm();
        onClose();
    };

    const handleModeChange = (newMode) => {
        resetForm();
        setMode(newMode);
    };

    const validateForm = () => {
        if (!email.trim()) {
            setLocalError('Email is required');
            return false;
        }

        if (!isValidEmail(email)) {
            setLocalError('Please enter a valid email address');
            return false;
        }

        if (mode !== 'reset') {
            if (!password) {
                setLocalError('Password is required');
                return false;
            }

            if (mode === 'signup') {
                const passwordValidation = validatePassword(password);
                if (!passwordValidation.isValid) {
                    setLocalError(passwordValidation.error);
                    return false;
                }

                if (password !== confirmPassword) {
                    setLocalError('Passwords do not match');
                    return false;
                }
            }
        }

        return true;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        setLocalError('');
        setSuccess('');

        try {
            if (mode === 'signin') {
                await signIn(email, password);
                handleClose();
            } else if (mode === 'signup') {
                await signUp(email, password);
                setSuccess('Account created successfully! Please check your email to verify your account.');
                // Don't close modal immediately for signup to show success message
            } else if (mode === 'reset') {
                await resetPassword(email);
                setSuccess('Password reset email sent! Please check your inbox.');
            }
        } catch (error) {
            // Error is handled by the auth context
            console.error('Auth error:', error);
        }
    };

    if (!isOpen) return null;

    const currentError = localError || error;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg w-full max-w-md">
                {/* Header */}
                <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
                    <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                        {mode === 'signin' && 'Sign In to VirSnapp'}
                        {mode === 'signup' && 'Create VirSnapp Account'}
                        {mode === 'reset' && 'Reset Password'}
                    </h2>
                    <button
                        onClick={handleClose}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                        <AiOutlineClose size={24} />
                    </button>
                </div>

                {/* Body */}
                <div className="p-6">
                    {/* Success Message */}
                    {success && (
                        <div className="mb-4 p-3 bg-green-100 dark:bg-green-900/20 border border-green-300 dark:border-green-700 rounded-md text-green-700 dark:text-green-400">
                            {success}
                        </div>
                    )}

                    {/* Error Message */}
                    {currentError && (
                        <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md text-red-700 dark:text-red-400">
                            {currentError}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="space-y-4">
                        {/* Email Field */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email Address
                            </label>
                            <input
                                type="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                                placeholder="Enter your email"
                                disabled={loading}
                                required
                            />
                        </div>

                        {/* Password Field */}
                        {mode !== 'reset' && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Password
                                </label>
                                <div className="relative">
                                    <input
                                        type={showPassword ? 'text' : 'password'}
                                        value={password}
                                        onChange={(e) => setPassword(e.target.value)}
                                        className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                                        placeholder="Enter your password"
                                        disabled={loading}
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                    >
                                        {showPassword ? <AiOutlineEyeInvisible size={20} /> : <AiOutlineEye size={20} />}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Confirm Password Field */}
                        {mode === 'signup' && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Confirm Password
                                </label>
                                <div className="relative">
                                    <input
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                                        placeholder="Confirm your password"
                                        disabled={loading}
                                        required
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                                    >
                                        {showConfirmPassword ? <AiOutlineEyeInvisible size={20} /> : <AiOutlineEye size={20} />}
                                    </button>
                                </div>
                            </div>
                        )}

                        {/* Submit Button */}
                        <button
                            type="submit"
                            disabled={loading}
                            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium transition-colors flex items-center justify-center gap-2"
                        >
                            {loading ? (
                                <BiLoaderAlt className="animate-spin" size={20} />
                            ) : (
                                <>
                                    {mode === 'signin' && 'Sign In'}
                                    {mode === 'signup' && 'Create Account'}
                                    {mode === 'reset' && 'Send Reset Email'}
                                </>
                            )}
                        </button>
                    </form>

                    {/* Mode Switching */}
                    <div className="mt-6 text-center space-y-2">
                        {mode === 'signin' && (
                            <>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Don't have an account?{' '}
                                    <button
                                        onClick={() => handleModeChange('signup')}
                                        className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
                                    >
                                        Sign up
                                    </button>
                                </p>
                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                    Forgot your password?{' '}
                                    <button
                                        onClick={() => handleModeChange('reset')}
                                        className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
                                    >
                                        Reset it
                                    </button>
                                </p>
                            </>
                        )}
                        
                        {mode === 'signup' && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Already have an account?{' '}
                                <button
                                    onClick={() => handleModeChange('signin')}
                                    className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
                                >
                                    Sign in
                                </button>
                            </p>
                        )}
                        
                        {mode === 'reset' && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                                Remember your password?{' '}
                                <button
                                    onClick={() => handleModeChange('signin')}
                                    className="text-blue-600 dark:text-blue-400 hover:underline font-medium"
                                >
                                    Sign in
                                </button>
                            </p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AuthModal;
