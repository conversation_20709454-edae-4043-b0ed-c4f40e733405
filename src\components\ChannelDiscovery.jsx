import React, { useState, useContext } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import { AiOutlineSearch, AiOutlinePlus } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';
import { BsFillCheckCircleFill } from 'react-icons/bs';

const ChannelDiscovery = ({ onAddToList }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [searchResults, setSearchResults] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [selectedChannels, setSelectedChannels] = useState(new Set());

    const { searchForChannels, channelLists } = useContext(Context);
    const { user } = useAuth();

    const handleSearch = async (e) => {
        e.preventDefault();
        
        if (!searchQuery.trim()) {
            setError('Please enter a search term');
            return;
        }

        try {
            setLoading(true);
            setError('');
            setSearchResults([]);
            
            const results = await searchForChannels(searchQuery.trim());
            
            // Filter for channels only
            const channels = results.contents?.filter(item => item.type === 'channel') || [];
            setSearchResults(channels);
            
            if (channels.length === 0) {
                setError('No channels found for your search term');
            }
        } catch (error) {
            console.error('Search error:', error);
            setError('Failed to search channels. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const formatSubscriberCount = (count) => {
        if (!count) return 'Unknown';
        if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
        if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
        return count.toString();
    };

    const toggleChannelSelection = (channel) => {
        const newSelected = new Set(selectedChannels);
        const channelKey = channel.channel?.channelId || channel.channel?.id;
        
        if (newSelected.has(channelKey)) {
            newSelected.delete(channelKey);
        } else {
            newSelected.add(channelKey);
        }
        
        setSelectedChannels(newSelected);
    };

    const handleAddSelectedToList = (listId) => {
        const channelsToAdd = searchResults
            .filter(result => {
                const channelKey = result.channel?.channelId || result.channel?.id;
                return selectedChannels.has(channelKey);
            })
            .map(result => ({
                id: result.channel?.channelId || result.channel?.id,
                name: result.channel?.title,
                thumbnail: result.channel?.avatar?.[0]?.url,
                subscriberCount: result.channel?.stats?.subscribersText,
                videoCount: result.channel?.stats?.videosText,
                url: `https://www.youtube.com/channel/${result.channel?.channelId || result.channel?.id}`
            }));

        if (onAddToList) {
            onAddToList(listId, channelsToAdd);
        }
        
        setSelectedChannels(new Set());
    };

    const openChannelInNewTab = (channel) => {
        const channelId = channel.channel?.channelId || channel.channel?.id;
        if (channelId) {
            window.open(`https://www.youtube.com/channel/${channelId}`, '_blank', 'noopener,noreferrer');
        }
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <div className="mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                    Discover Channels
                </h2>
                
                {/* Search Form */}
                <form onSubmit={handleSearch} className="flex gap-2">
                    <div className="flex-1 relative">
                        <AiOutlineSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                        <input
                            type="text"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            placeholder="Search for YouTube channels..."
                            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                            disabled={loading}
                        />
                    </div>
                    <button
                        type="submit"
                        disabled={loading || !searchQuery.trim()}
                        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md font-medium transition-colors flex items-center gap-2"
                    >
                        {loading ? (
                            <BiLoaderAlt className="animate-spin" size={20} />
                        ) : (
                            <>
                                <AiOutlineSearch size={20} />
                                Search
                            </>
                        )}
                    </button>
                </form>
            </div>

            {/* Error Message */}
            {error && (
                <div className="mb-6 p-4 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md text-red-700 dark:text-red-400">
                    {error}
                </div>
            )}

            {/* Bulk Actions */}
            {selectedChannels.size > 0 && user && channelLists.length > 0 && (
                <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-md">
                    <div className="flex flex-wrap items-center gap-4">
                        <span className="text-blue-800 dark:text-blue-200 font-medium">
                            {selectedChannels.size} channel{selectedChannels.size !== 1 ? 's' : ''} selected
                        </span>
                        <div className="flex flex-wrap gap-2">
                            {channelLists.map(list => (
                                <button
                                    key={list.id}
                                    onClick={() => handleAddSelectedToList(list.id)}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors flex items-center gap-1"
                                >
                                    <AiOutlinePlus size={14} />
                                    Add to {list.name}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Search Results */}
            <div className="space-y-4">
                {loading && (
                    <div className="flex justify-center py-12">
                        <BiLoaderAlt className="animate-spin text-blue-600" size={32} />
                    </div>
                )}

                {!loading && searchResults.length > 0 && (
                    <div className="grid gap-4">
                        {searchResults.map((result, index) => {
                            const channel = result.channel;
                            const channelKey = channel?.channelId || channel?.id;
                            const isSelected = selectedChannels.has(channelKey);
                            
                            return (
                                <div
                                    key={channelKey || index}
                                    className={`border rounded-lg p-4 transition-colors ${
                                        isSelected 
                                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                                            : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                                    }`}
                                >
                                    <div className="flex items-start gap-4">
                                        {/* Selection Checkbox */}
                                        {user && (
                                            <input
                                                type="checkbox"
                                                checked={isSelected}
                                                onChange={() => toggleChannelSelection(result)}
                                                className="mt-2 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                            />
                                        )}

                                        {/* Channel Thumbnail */}
                                        <div className="flex-shrink-0">
                                            {channel?.avatar?.[0]?.url ? (
                                                <img
                                                    src={channel.avatar[0].url}
                                                    alt={channel.title}
                                                    className="w-16 h-16 rounded-full object-cover"
                                                />
                                            ) : (
                                                <div className="w-16 h-16 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                                                    <span className="text-gray-500 dark:text-gray-400 text-xl font-bold">
                                                        {channel?.title?.charAt(0)?.toUpperCase() || '?'}
                                                    </span>
                                                </div>
                                            )}
                                        </div>

                                        {/* Channel Info */}
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1 min-w-0">
                                                    <h3 className="font-semibold text-gray-900 dark:text-white text-lg truncate flex items-center gap-2">
                                                        {channel?.title}
                                                        {channel?.badges?.some(badge => badge.type === 'VERIFIED_CHANNEL') && (
                                                            <BsFillCheckCircleFill className="text-blue-500 text-sm flex-shrink-0" />
                                                        )}
                                                    </h3>
                                                    
                                                    {/* Channel Stats */}
                                                    <div className="flex flex-wrap gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                                                        <span>{channel?.stats?.subscribersText || 'Unknown subscribers'}</span>
                                                        <span>{channel?.stats?.videosText || 'Unknown videos'}</span>
                                                    </div>

                                                    {/* Channel Description */}
                                                    {channel?.descriptionSnippet && (
                                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 line-clamp-2">
                                                            {channel.descriptionSnippet}
                                                        </p>
                                                    )}
                                                </div>

                                                {/* View Channel Button */}
                                                <button
                                                    onClick={() => openChannelInNewTab(result)}
                                                    className="ml-4 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium"
                                                >
                                                    View Channel
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                )}

                {!loading && searchQuery && searchResults.length === 0 && !error && (
                    <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                        <AiOutlineSearch size={48} className="mx-auto mb-4 opacity-50" />
                        <p>No channels found for "{searchQuery}"</p>
                        <p className="text-sm mt-2">Try different search terms or check your spelling.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ChannelDiscovery;
