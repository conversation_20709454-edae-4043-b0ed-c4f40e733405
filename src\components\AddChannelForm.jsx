import React, { useState, useContext } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import { fetchChannelDetails } from '../utils/api';
import { isValidYouTubeChannelUrl, normalizeYouTubeUrl, extractChannelIdFromUrl } from '../utils/validation';
import { AiOutlinePlus, AiOutlineClose } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';

const AddChannelForm = ({ selectedListId, onClose, onChannelAdded }) => {
    const [channelUrl, setChannelUrl] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [channelPreview, setChannelPreview] = useState(null);
    
    const { addChannelToList } = useContext(Context);
    const { user } = useAuth();

    const handleUrlChange = (e) => {
        const url = e.target.value;
        setChannelUrl(url);
        setError('');
        setChannelPreview(null);
    };

    const validateAndPreviewChannel = async () => {
        if (!channelUrl.trim()) {
            setError('Please enter a YouTube channel URL');
            return;
        }

        const normalizedUrl = normalizeYouTubeUrl(channelUrl);
        
        if (!isValidYouTubeChannelUrl(normalizedUrl)) {
            setError('Please enter a valid YouTube channel URL');
            return;
        }

        try {
            setLoading(true);
            setError('');
            
            const channelData = await fetchChannelDetails(normalizedUrl);
            
            if (channelData && channelData.meta) {
                setChannelPreview({
                    id: channelData.meta.channelId,
                    name: channelData.meta.title,
                    thumbnail: channelData.meta.image?.url,
                    subscriberCount: channelData.meta.subscriberCount,
                    videoCount: channelData.meta.videoCount,
                    description: channelData.meta.description,
                    url: normalizedUrl
                });
            } else {
                setError('Could not fetch channel information. Please check the URL.');
            }
        } catch (error) {
            console.error('Error fetching channel details:', error);
            setError('Failed to fetch channel information. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleAddChannel = async () => {
        if (!channelPreview || !selectedListId) {
            setError('Please select a channel and list');
            return;
        }

        try {
            setLoading(true);
            setError('');
            
            await addChannelToList(selectedListId, channelPreview);
            
            // Reset form
            setChannelUrl('');
            setChannelPreview(null);
            
            if (onChannelAdded) {
                onChannelAdded(channelPreview);
            }
            
            if (onClose) {
                onClose();
            }
        } catch (error) {
            console.error('Error adding channel:', error);
            setError('Failed to add channel. It may already exist in this list.');
        } finally {
            setLoading(false);
        }
    };

    const formatSubscriberCount = (count) => {
        if (!count) return 'Unknown';
        if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
        if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
        return count.toString();
    };

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-auto">
            <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Add YouTube Channel
                </h3>
                {onClose && (
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                    >
                        <AiOutlineClose size={20} />
                    </button>
                )}
            </div>

            <div className="space-y-4">
                <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        YouTube Channel URL
                    </label>
                    <input
                        type="url"
                        value={channelUrl}
                        onChange={handleUrlChange}
                        placeholder="https://www.youtube.com/@channelname"
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        disabled={loading}
                    />
                </div>

                {error && (
                    <div className="text-red-600 dark:text-red-400 text-sm">
                        {error}
                    </div>
                )}

                <div className="flex gap-2">
                    <button
                        onClick={validateAndPreviewChannel}
                        disabled={loading || !channelUrl.trim()}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center justify-center gap-2"
                    >
                        {loading ? (
                            <BiLoaderAlt className="animate-spin" size={16} />
                        ) : (
                            'Preview Channel'
                        )}
                    </button>
                </div>

                {channelPreview && (
                    <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                        <div className="flex items-start gap-3">
                            {channelPreview.thumbnail && (
                                <img
                                    src={channelPreview.thumbnail}
                                    alt={channelPreview.name}
                                    className="w-12 h-12 rounded-full object-cover"
                                />
                            )}
                            <div className="flex-1 min-w-0">
                                <h4 className="font-medium text-gray-900 dark:text-white truncate">
                                    {channelPreview.name}
                                </h4>
                                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                    <div>
                                        {formatSubscriberCount(channelPreview.subscriberCount)} subscribers
                                    </div>
                                    <div>
                                        {channelPreview.videoCount || 0} videos
                                    </div>
                                </div>
                                {channelPreview.description && (
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 line-clamp-2">
                                        {channelPreview.description}
                                    </p>
                                )}
                            </div>
                        </div>
                        
                        <button
                            onClick={handleAddChannel}
                            disabled={loading}
                            className="w-full mt-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center justify-center gap-2"
                        >
                            {loading ? (
                                <BiLoaderAlt className="animate-spin" size={16} />
                            ) : (
                                <>
                                    <AiOutlinePlus size={16} />
                                    Add to List
                                </>
                            )}
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AddChannelForm;
