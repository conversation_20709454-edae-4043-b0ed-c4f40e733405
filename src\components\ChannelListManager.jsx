import React, { useState, useContext } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import { validateChannelListName } from '../utils/validation';
import { AiOutlinePlus, AiOutlineEdit, AiOutlineDelete, AiOutlineFolder } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';

const ChannelListManager = ({ onListSelect, selectedListId }) => {
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [editingList, setEditingList] = useState(null);
    const [newListName, setNewListName] = useState('');
    const [newListDescription, setNewListDescription] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    const { 
        channelLists, 
        createChannelList, 
        updateChannelList, 
        deleteChannelList,
        loading: contextLoading 
    } = useContext(Context);
    const { user } = useAuth();

    const handleCreateList = async (e) => {
        e.preventDefault();
        
        const validation = validateChannelListName(newListName);
        if (!validation.isValid) {
            setError(validation.error);
            return;
        }

        try {
            setLoading(true);
            setError('');
            
            const newList = await createChannelList(newListName.trim(), newListDescription.trim());
            
            // Reset form
            setNewListName('');
            setNewListDescription('');
            setShowCreateForm(false);
            
            // Select the new list
            if (onListSelect) {
                onListSelect(newList);
            }
        } catch (error) {
            console.error('Error creating list:', error);
            setError('Failed to create list. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleUpdateList = async (e) => {
        e.preventDefault();
        
        if (!editingList) return;
        
        const validation = validateChannelListName(newListName);
        if (!validation.isValid) {
            setError(validation.error);
            return;
        }

        try {
            setLoading(true);
            setError('');
            
            await updateChannelList(editingList.id, {
                name: newListName.trim(),
                description: newListDescription.trim()
            });
            
            // Reset form
            setEditingList(null);
            setNewListName('');
            setNewListDescription('');
        } catch (error) {
            console.error('Error updating list:', error);
            setError('Failed to update list. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleDeleteList = async (listId, listName) => {
        if (!window.confirm(`Are you sure you want to delete "${listName}"? This action cannot be undone.`)) {
            return;
        }

        try {
            setLoading(true);
            setError('');
            
            await deleteChannelList(listId);
            
            // If the deleted list was selected, clear selection
            if (selectedListId === listId && onListSelect) {
                onListSelect(null);
            }
        } catch (error) {
            console.error('Error deleting list:', error);
            setError('Failed to delete list. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const startEditing = (list) => {
        setEditingList(list);
        setNewListName(list.name);
        setNewListDescription(list.description || '');
        setShowCreateForm(false);
        setError('');
    };

    const cancelEditing = () => {
        setEditingList(null);
        setNewListName('');
        setNewListDescription('');
        setError('');
    };

    const startCreating = () => {
        setShowCreateForm(true);
        setEditingList(null);
        setNewListName('');
        setNewListDescription('');
        setError('');
    };

    const cancelCreating = () => {
        setShowCreateForm(false);
        setNewListName('');
        setNewListDescription('');
        setError('');
    };

    if (!user) {
        return (
            <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                Please sign in to manage your channel lists.
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    My Channel Lists
                </h2>
                <button
                    onClick={startCreating}
                    disabled={loading || contextLoading}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
                >
                    <AiOutlinePlus size={16} />
                    New List
                </button>
            </div>

            {error && (
                <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md text-red-700 dark:text-red-400">
                    {error}
                </div>
            )}

            {/* Create/Edit Form */}
            {(showCreateForm || editingList) && (
                <form 
                    onSubmit={editingList ? handleUpdateList : handleCreateList}
                    className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                >
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        {editingList ? 'Edit List' : 'Create New List'}
                    </h3>
                    
                    <div className="space-y-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                List Name *
                            </label>
                            <input
                                type="text"
                                value={newListName}
                                onChange={(e) => setNewListName(e.target.value)}
                                placeholder="Enter list name"
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
                                required
                                maxLength={100}
                                disabled={loading}
                            />
                        </div>
                        
                        <div>
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Description (Optional)
                            </label>
                            <textarea
                                value={newListDescription}
                                onChange={(e) => setNewListDescription(e.target.value)}
                                placeholder="Enter list description"
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white resize-none"
                                maxLength={500}
                                disabled={loading}
                            />
                        </div>
                        
                        <div className="flex gap-2">
                            <button
                                type="submit"
                                disabled={loading || !newListName.trim()}
                                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
                            >
                                {loading ? (
                                    <BiLoaderAlt className="animate-spin" size={16} />
                                ) : (
                                    editingList ? 'Update List' : 'Create List'
                                )}
                            </button>
                            <button
                                type="button"
                                onClick={editingList ? cancelEditing : cancelCreating}
                                disabled={loading}
                                className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </form>
            )}

            {/* Lists Display */}
            <div className="space-y-3">
                {contextLoading ? (
                    <div className="flex justify-center py-8">
                        <BiLoaderAlt className="animate-spin text-blue-600" size={32} />
                    </div>
                ) : channelLists.length === 0 ? (
                    <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        <AiOutlineFolder size={48} className="mx-auto mb-4 opacity-50" />
                        <p>No channel lists yet.</p>
                        <p className="text-sm">Create your first list to get started!</p>
                    </div>
                ) : (
                    channelLists.map((list) => (
                        <div
                            key={list.id}
                            className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                                selectedListId === list.id
                                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                                    : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                            }`}
                            onClick={() => onListSelect && onListSelect(list)}
                        >
                            <div className="flex justify-between items-start">
                                <div className="flex-1 min-w-0">
                                    <h3 className="font-medium text-gray-900 dark:text-white truncate">
                                        {list.name}
                                    </h3>
                                    {list.description && (
                                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1 line-clamp-2">
                                            {list.description}
                                        </p>
                                    )}
                                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                                        {list.channel_list_items?.length || 0} channels
                                    </div>
                                </div>
                                
                                <div className="flex gap-2 ml-4">
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            startEditing(list);
                                        }}
                                        disabled={loading}
                                        className="text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400 p-1"
                                        title="Edit list"
                                    >
                                        <AiOutlineEdit size={16} />
                                    </button>
                                    <button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteList(list.id, list.name);
                                        }}
                                        disabled={loading}
                                        className="text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 p-1"
                                        title="Delete list"
                                    >
                                        <AiOutlineDelete size={16} />
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};

export default ChannelListManager;
