// URL validation utilities for VirSnapp

/**
 * Validates if a URL is a valid YouTube channel URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if valid YouTube channel URL
 */
export const isValidYouTubeChannelUrl = (url) => {
    if (!url || typeof url !== 'string') return false;
    
    const patterns = [
        /^https?:\/\/(www\.)?youtube\.com\/channel\/[a-zA-Z0-9_-]+\/?$/,
        /^https?:\/\/(www\.)?youtube\.com\/c\/[a-zA-Z0-9_-]+\/?$/,
        /^https?:\/\/(www\.)?youtube\.com\/user\/[a-zA-Z0-9_-]+\/?$/,
        /^https?:\/\/(www\.)?youtube\.com\/@[a-zA-Z0-9_-]+\/?$/,
    ];
    
    return patterns.some(pattern => pattern.test(url.trim()));
};

/**
 * Extracts channel identifier from YouTube URL
 * @param {string} url - YouTube channel URL
 * @returns {string} - Channel identifier
 */
export const extractChannelIdFromUrl = (url) => {
    if (!isValidYouTubeChannelUrl(url)) {
        throw new Error('Invalid YouTube channel URL format');
    }
    
    const patterns = [
        { regex: /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/, type: 'channel' },
        { regex: /youtube\.com\/c\/([a-zA-Z0-9_-]+)/, type: 'custom' },
        { regex: /youtube\.com\/user\/([a-zA-Z0-9_-]+)/, type: 'user' },
        { regex: /youtube\.com\/@([a-zA-Z0-9_-]+)/, type: 'handle' },
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern.regex);
        if (match) {
            return {
                id: match[1],
                type: pattern.type
            };
        }
    }
    
    throw new Error('Could not extract channel ID from URL');
};

/**
 * Normalizes YouTube channel URL to a standard format
 * @param {string} url - YouTube channel URL
 * @returns {string} - Normalized URL
 */
export const normalizeYouTubeUrl = (url) => {
    if (!url) return '';
    
    let normalizedUrl = url.trim();
    
    // Add protocol if missing
    if (!normalizedUrl.startsWith('http')) {
        normalizedUrl = 'https://' + normalizedUrl;
    }
    
    // Remove trailing slash
    normalizedUrl = normalizedUrl.replace(/\/$/, '');
    
    // Ensure www. is present for consistency
    normalizedUrl = normalizedUrl.replace(/https:\/\/youtube\.com/, 'https://www.youtube.com');
    
    return normalizedUrl;
};

/**
 * Validates channel list name
 * @param {string} name - List name to validate
 * @returns {object} - Validation result with isValid and error message
 */
export const validateChannelListName = (name) => {
    if (!name || typeof name !== 'string') {
        return { isValid: false, error: 'List name is required' };
    }
    
    const trimmedName = name.trim();
    
    if (trimmedName.length === 0) {
        return { isValid: false, error: 'List name cannot be empty' };
    }
    
    if (trimmedName.length > 100) {
        return { isValid: false, error: 'List name must be 100 characters or less' };
    }
    
    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(trimmedName)) {
        return { isValid: false, error: 'List name contains invalid characters' };
    }
    
    return { isValid: true, error: null };
};

/**
 * Validates email format
 * @param {string} email - Email to validate
 * @returns {boolean} - True if valid email format
 */
export const isValidEmail = (email) => {
    if (!email || typeof email !== 'string') return false;
    
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
};

/**
 * Validates password strength
 * @param {string} password - Password to validate
 * @returns {object} - Validation result with isValid, strength, and requirements
 */
export const validatePassword = (password) => {
    if (!password || typeof password !== 'string') {
        return {
            isValid: false,
            strength: 'weak',
            requirements: {
                length: false,
                uppercase: false,
                lowercase: false,
                number: false,
                special: false
            },
            error: 'Password is required'
        };
    }
    
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    const metRequirements = Object.values(requirements).filter(Boolean).length;
    
    let strength = 'weak';
    if (metRequirements >= 4) strength = 'strong';
    else if (metRequirements >= 3) strength = 'medium';
    
    const isValid = requirements.length && requirements.lowercase && 
                   (requirements.uppercase || requirements.number || requirements.special);
    
    return {
        isValid,
        strength,
        requirements,
        error: isValid ? null : 'Password must be at least 8 characters with lowercase letters and at least one uppercase, number, or special character'
    };
};

/**
 * Sanitizes user input to prevent XSS
 * @param {string} input - Input to sanitize
 * @returns {string} - Sanitized input
 */
export const sanitizeInput = (input) => {
    if (!input || typeof input !== 'string') return '';

    return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;')
        .replace(/\//g, '&#x2F;')
        .trim();
};

/**
 * Parses YouTube subscriber/video count strings into numbers
 * @param {string} countText - Text like "2.43K subscribers" or "1.2M videos"
 * @returns {object} - { text: original, raw: number }
 */
export const parseYouTubeCount = (countText) => {
    if (!countText || typeof countText !== 'string') {
        return { text: 'Unknown', raw: 0 };
    }

    const cleanText = countText.trim();

    // Extract number and multiplier
    const match = cleanText.match(/^([\d,.]+)([KMB]?)/i);

    if (!match) {
        // Try to extract just numbers
        const numberMatch = cleanText.match(/[\d,]+/);
        if (numberMatch) {
            const number = parseInt(numberMatch[0].replace(/,/g, ''), 10);
            return { text: cleanText, raw: isNaN(number) ? 0 : number };
        }
        return { text: cleanText, raw: 0 };
    }

    const [, numberPart, multiplier] = match;
    let number = parseFloat(numberPart.replace(/,/g, ''));

    if (isNaN(number)) {
        return { text: cleanText, raw: 0 };
    }

    // Apply multiplier
    switch (multiplier?.toUpperCase()) {
        case 'K':
            number *= 1000;
            break;
        case 'M':
            number *= 1000000;
            break;
        case 'B':
            number *= 1000000000;
            break;
    }

    return {
        text: cleanText,
        raw: Math.floor(number)
    };
};

/**
 * Formats a number as a YouTube-style count string
 * @param {number} count - Raw number
 * @returns {string} - Formatted string like "2.4K" or "1.2M"
 */
export const formatYouTubeCount = (count) => {
    if (!count || count === 0) return 'Unknown';

    if (count >= 1000000000) {
        return `${(count / 1000000000).toFixed(1)}B`;
    } else if (count >= 1000000) {
        return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
        return `${(count / 1000).toFixed(1)}K`;
    } else {
        return count.toString();
    }
};
