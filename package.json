{"name": "youtube", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.2.0", "@fortawesome/free-solid-svg-icons": "^6.2.0", "@fortawesome/react-fontawesome": "^0.2.0", "@iconscout/react-unicons": "^1.1.6", "@tailwindcss/line-clamp": "^0.4.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.1.3", "html-react-parser": "^3.0.4", "js-abbreviation-number": "^1.4.0", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.6.0", "react-lazy-load": "^4.0.1", "react-moment": "^1.1.2", "react-player": "^2.11.0", "react-router-dom": "^6.4.3", "react-scripts": "5.0.1", "react-tooltip": "^4.5.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.13", "postcss": "^8.4.19", "tailwindcss": "^3.2.4"}}