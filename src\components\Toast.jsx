import React, { useState, useEffect, createContext, useContext } from 'react';
import { AiOutlineClose, AiOutlineCheckCircle, AiOutlineWarning, AiOutlineInfoCircle, AiOutlineCloseCircle } from 'react-icons/ai';

const ToastContext = createContext();

export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};

export const ToastProvider = ({ children }) => {
    const [toasts, setToasts] = useState([]);

    const addToast = (message, type = 'info', duration = 5000) => {
        const id = Date.now() + Math.random();
        const toast = { id, message, type, duration };
        
        setToasts(prev => [...prev, toast]);

        if (duration > 0) {
            setTimeout(() => {
                removeToast(id);
            }, duration);
        }

        return id;
    };

    const removeToast = (id) => {
        setToasts(prev => prev.filter(toast => toast.id !== id));
    };

    const success = (message, duration) => addToast(message, 'success', duration);
    const error = (message, duration) => addToast(message, 'error', duration);
    const warning = (message, duration) => addToast(message, 'warning', duration);
    const info = (message, duration) => addToast(message, 'info', duration);

    return (
        <ToastContext.Provider value={{ addToast, removeToast, success, error, warning, info }}>
            {children}
            <ToastContainer toasts={toasts} onRemove={removeToast} />
        </ToastContext.Provider>
    );
};

const ToastContainer = ({ toasts, onRemove }) => {
    if (toasts.length === 0) return null;

    return (
        <div className="fixed top-4 right-4 z-50 space-y-2">
            {toasts.map(toast => (
                <Toast key={toast.id} toast={toast} onRemove={onRemove} />
            ))}
        </div>
    );
};

const Toast = ({ toast, onRemove }) => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        // Trigger animation
        const timer = setTimeout(() => setIsVisible(true), 10);
        return () => clearTimeout(timer);
    }, []);

    const handleRemove = () => {
        setIsVisible(false);
        setTimeout(() => onRemove(toast.id), 300);
    };

    const getToastStyles = () => {
        const baseStyles = "flex items-center gap-3 p-4 rounded-lg shadow-lg border transition-all duration-300 transform max-w-sm";
        
        if (!isVisible) {
            return `${baseStyles} translate-x-full opacity-0`;
        }

        switch (toast.type) {
            case 'success':
                return `${baseStyles} bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200`;
            case 'error':
                return `${baseStyles} bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200`;
            case 'warning':
                return `${baseStyles} bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200`;
            case 'info':
            default:
                return `${baseStyles} bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200`;
        }
    };

    const getIcon = () => {
        const iconProps = { size: 20, className: "flex-shrink-0" };
        
        switch (toast.type) {
            case 'success':
                return <AiOutlineCheckCircle {...iconProps} className="text-green-500" />;
            case 'error':
                return <AiOutlineCloseCircle {...iconProps} className="text-red-500" />;
            case 'warning':
                return <AiOutlineWarning {...iconProps} className="text-yellow-500" />;
            case 'info':
            default:
                return <AiOutlineInfoCircle {...iconProps} className="text-blue-500" />;
        }
    };

    return (
        <div className={getToastStyles()}>
            {getIcon()}
            <div className="flex-1 text-sm font-medium">
                {toast.message}
            </div>
            <button
                onClick={handleRemove}
                className="flex-shrink-0 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            >
                <AiOutlineClose size={16} />
            </button>
        </div>
    );
};

export default Toast;
