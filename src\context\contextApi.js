import React, { createContext, useState, useEffect } from "react";

import { fetchDataFromApi, searchChannels } from "../utils/api";
import { supabaseHelpers } from "../lib/supabase";
import { supabase } from "../lib/supabase";

export const Context = createContext();

export const AppContext = (props) => {
    const [loading, setLoading] = useState(false);
    const [searchResults, setSearchResults] = useState([]);
    const [selectedCategory, setSelectedCategory] = useState("New");
    const [mobileMenu, setMobileMenu] = useState(false);

    // VirSnapp specific state
    const [channelLists, setChannelLists] = useState([]);
    const [selectedList, setSelectedList] = useState(null);
    const [channelSearchResults, setChannelSearchResults] = useState([]);
    const [error, setError] = useState(null);
    const [user, setUser] = useState(null);

    // Listen for auth changes
    useEffect(() => {
        const { data: { subscription } } = supabase.auth.onAuthStateChange(
            (event, session) => {
                setUser(session?.user ?? null);
            }
        );

        // Get initial session
        supabase.auth.getSession().then(({ data: { session } }) => {
            setUser(session?.user ?? null);
        });

        return () => subscription.unsubscribe();
    }, []);

    useEffect(() => {
        if (selectedCategory && selectedCategory !== "New") {
            fetchSelectedCategoryData(selectedCategory);
        }
    }, [selectedCategory]);

    useEffect(() => {
        if (user) {
            loadUserChannelLists();
        } else {
            setChannelLists([]);
            setSelectedList(null);
        }
    }, [user]);

    const fetchSelectedCategoryData = async (query) => {
        try {
            setLoading(true);
            setError(null);
            const data = await fetchDataFromApi(`search/?q=${query}`);
            setSearchResults(data.contents || []);
        } catch (error) {
            console.error("Error fetching category data:", error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    // VirSnapp specific functions
    const loadUserChannelLists = async () => {
        if (!user) return;

        try {
            setLoading(true);
            const lists = await supabaseHelpers.getUserChannelLists(user.id);
            setChannelLists(lists);
        } catch (error) {
            console.error("Error loading channel lists:", error);
            setError(error.message);
        } finally {
            setLoading(false);
        }
    };

    const createChannelList = async (name, description = '') => {
        if (!user) throw new Error("User must be authenticated");

        try {
            setLoading(true);
            const newList = await supabaseHelpers.createChannelList(user.id, name, description);
            setChannelLists(prev => [newList, ...prev]);
            return newList;
        } catch (error) {
            console.error("Error creating channel list:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const updateChannelList = async (listId, updates) => {
        try {
            setLoading(true);
            const updatedList = await supabaseHelpers.updateChannelList(listId, updates);
            setChannelLists(prev =>
                prev.map(list => list.id === listId ? updatedList : list)
            );
            return updatedList;
        } catch (error) {
            console.error("Error updating channel list:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const deleteChannelList = async (listId) => {
        try {
            setLoading(true);
            await supabaseHelpers.deleteChannelList(listId);
            setChannelLists(prev => prev.filter(list => list.id !== listId));
            if (selectedList?.id === listId) {
                setSelectedList(null);
            }
        } catch (error) {
            console.error("Error deleting channel list:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const searchForChannels = async (query) => {
        try {
            setLoading(true);
            setError(null);
            const results = await searchChannels(query);
            setChannelSearchResults(results.contents || []);
            return results;
        } catch (error) {
            console.error("Error searching channels:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const addChannelToList = async (listId, channelData) => {
        try {
            setLoading(true);
            await supabaseHelpers.addChannelToList(listId, channelData);
            await loadUserChannelLists(); // Refresh lists
        } catch (error) {
            console.error("Error adding channel to list:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const removeChannelFromList = async (itemId) => {
        try {
            setLoading(true);
            await supabaseHelpers.removeChannelFromList(itemId);
            await loadUserChannelLists(); // Refresh lists
        } catch (error) {
            console.error("Error removing channel from list:", error);
            setError(error.message);
            throw error;
        } finally {
            setLoading(false);
        }
    };

    const clearError = () => setError(null);

    return (
        <Context.Provider
            value={{
                // Original YouTube clone state
                loading,
                setLoading,
                searchResults,
                selectedCategory,
                setSelectedCategory,
                mobileMenu,
                setMobileMenu,

                // VirSnapp specific state and functions
                channelLists,
                selectedList,
                setSelectedList,
                channelSearchResults,
                error,
                clearError,

                // VirSnapp functions
                loadUserChannelLists,
                createChannelList,
                updateChannelList,
                deleteChannelList,
                searchForChannels,
                addChannelToList,
                removeChannelFromList,
            }}
        >
            {props.children}
        </Context.Provider>
    );
};
