// Basic validation tests for VirSnapp
// Run with: npm test

import { 
    isValidYouTubeChannelUrl, 
    extractChannelIdFromUrl, 
    normalizeYouTubeUrl,
    validateChannelListName,
    isValidEmail,
    validatePassword
} from './validation';

// YouTube URL Validation Tests
describe('YouTube URL Validation', () => {
    test('validates correct YouTube channel URLs', () => {
        const validUrls = [
            'https://www.youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw',
            'https://youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw',
            'http://www.youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw',
            'https://www.youtube.com/c/LinusTechTips',
            'https://www.youtube.com/user/LinusTechTips',
            'https://www.youtube.com/@LinusTechTips'
        ];

        validUrls.forEach(url => {
            expect(isValidYouTubeChannelUrl(url)).toBe(true);
        });
    });

    test('rejects invalid YouTube URLs', () => {
        const invalidUrls = [
            'https://www.google.com',
            'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'https://www.youtube.com/playlist?list=PLrAXtmRdnEQy',
            'not-a-url',
            '',
            null,
            undefined
        ];

        invalidUrls.forEach(url => {
            expect(isValidYouTubeChannelUrl(url)).toBe(false);
        });
    });

    test('extracts channel ID from various URL formats', () => {
        const testCases = [
            {
                url: 'https://www.youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw',
                expected: { id: 'UCXuqSBlHAE6Xw-yeJA0Tunw', type: 'channel' }
            },
            {
                url: 'https://www.youtube.com/c/LinusTechTips',
                expected: { id: 'LinusTechTips', type: 'custom' }
            },
            {
                url: 'https://www.youtube.com/user/LinusTechTips',
                expected: { id: 'LinusTechTips', type: 'user' }
            },
            {
                url: 'https://www.youtube.com/@LinusTechTips',
                expected: { id: 'LinusTechTips', type: 'handle' }
            }
        ];

        testCases.forEach(({ url, expected }) => {
            expect(extractChannelIdFromUrl(url)).toEqual(expected);
        });
    });

    test('normalizes YouTube URLs correctly', () => {
        const testCases = [
            {
                input: 'youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw',
                expected: 'https://www.youtube.com/channel/UCXuqSBlHAE6Xw-yeJA0Tunw'
            },
            {
                input: 'https://youtube.com/c/LinusTechTips/',
                expected: 'https://www.youtube.com/c/LinusTechTips'
            },
            {
                input: 'http://www.youtube.com/user/test',
                expected: 'http://www.youtube.com/user/test'
            }
        ];

        testCases.forEach(({ input, expected }) => {
            expect(normalizeYouTubeUrl(input)).toBe(expected);
        });
    });
});

// Channel List Name Validation Tests
describe('Channel List Name Validation', () => {
    test('validates correct list names', () => {
        const validNames = [
            'My Favorite Channels',
            'Tech Reviews',
            'Gaming Content',
            'A',
            'a'.repeat(100) // Max length
        ];

        validNames.forEach(name => {
            const result = validateChannelListName(name);
            expect(result.isValid).toBe(true);
            expect(result.error).toBeNull();
        });
    });

    test('rejects invalid list names', () => {
        const invalidNames = [
            '', // Empty
            '   ', // Whitespace only
            null,
            undefined,
            'a'.repeat(101), // Too long
            'Invalid<>Name', // Invalid characters
            'Bad/Name',
            'Bad\\Name'
        ];

        invalidNames.forEach(name => {
            const result = validateChannelListName(name);
            expect(result.isValid).toBe(false);
            expect(result.error).toBeTruthy();
        });
    });
});

// Email Validation Tests
describe('Email Validation', () => {
    test('validates correct email addresses', () => {
        const validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        validEmails.forEach(email => {
            expect(isValidEmail(email)).toBe(true);
        });
    });

    test('rejects invalid email addresses', () => {
        const invalidEmails = [
            'invalid-email',
            '@domain.com',
            'user@',
            'user@domain',
            '',
            null,
            undefined
        ];

        invalidEmails.forEach(email => {
            expect(isValidEmail(email)).toBe(false);
        });
    });
});

// Password Validation Tests
describe('Password Validation', () => {
    test('validates strong passwords', () => {
        const strongPasswords = [
            'StrongPass123!',
            'MySecure@Password1',
            'Complex#Pass99'
        ];

        strongPasswords.forEach(password => {
            const result = validatePassword(password);
            expect(result.isValid).toBe(true);
            expect(result.strength).toBe('strong');
            expect(result.error).toBeNull();
        });
    });

    test('identifies weak passwords', () => {
        const weakPasswords = [
            'weak',
            '12345678',
            'password',
            'PASSWORD',
            'Pass123' // Too short
        ];

        weakPasswords.forEach(password => {
            const result = validatePassword(password);
            expect(result.strength).toBe('weak');
        });
    });

    test('requires minimum length and character types', () => {
        const result = validatePassword('short');
        expect(result.isValid).toBe(false);
        expect(result.requirements.length).toBe(false);
    });
});

// Console log for manual testing
console.log('VirSnapp Validation Tests');
console.log('========================');
console.log('Run these tests with: npm test');
console.log('');
console.log('Manual Test Examples:');
console.log('- Valid YouTube URL:', isValidYouTubeChannelUrl('https://www.youtube.com/@LinusTechTips'));
console.log('- Invalid YouTube URL:', isValidYouTubeChannelUrl('https://www.google.com'));
console.log('- Valid Email:', isValidEmail('<EMAIL>'));
console.log('- Invalid Email:', isValidEmail('invalid-email'));
console.log('- Valid List Name:', validateChannelListName('My Tech Channels'));
console.log('- Invalid List Name:', validateChannelListName(''));

export default {
    isValidYouTubeChannelUrl,
    extractChannelIdFromUrl,
    normalizeYouTubeUrl,
    validateChannelListName,
    isValidEmail,
    validatePassword
};
