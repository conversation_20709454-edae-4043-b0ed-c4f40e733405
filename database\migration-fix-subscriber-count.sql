-- Migration to fix subscriber count data type issue
-- Run this if you already have the channel_list_items table created

-- Step 1: Add new columns for text-based counts
ALTER TABLE channel_list_items 
ADD COLUMN IF NOT EXISTS subscriber_count_text VARCHAR(100) DEFAULT 'Unknown',
ADD COLUMN IF NOT EXISTS subscriber_count_raw BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS video_count_text VARCHAR(100) DEFAULT 'Unknown',
ADD COLUMN IF NOT EXISTS video_count_raw BIGINT DEFAULT 0;

-- Step 2: Migrate existing data (if any)
UPDATE channel_list_items 
SET subscriber_count_raw = subscriber_count,
    video_count_raw = video_count
WHERE subscriber_count IS NOT NULL OR video_count IS NOT NULL;

-- Step 3: Drop old columns (optional - uncomment if you want to remove them)
-- ALTER TABLE channel_list_items DROP COLUMN IF EXISTS subscriber_count;
-- ALTER TABLE channel_list_items DROP COLUMN IF EXISTS video_count;

-- Step 4: Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'channel_list_items' 
AND column_name LIKE '%count%'
ORDER BY column_name;
