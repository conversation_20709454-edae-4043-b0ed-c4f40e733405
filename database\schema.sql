-- VirSnapp Database Schema for Supabase
-- Run this SQL in your Supabase SQL editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create channel_lists table
CREATE TABLE IF NOT EXISTS channel_lists (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHA<PERSON>(255) NOT NULL,
    description TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create channel_list_items table
CREATE TABLE IF NOT EXISTS channel_list_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    list_id UUID REFERENCES channel_lists(id) ON DELETE CASCADE NOT NULL,
    channel_url TEXT NOT NULL,
    channel_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    channel_id VARCHAR(255) NOT NULL,
    channel_thumbnail TEXT,
    subscriber_count BIGINT DEFAULT 0,
    video_count BIGINT DEFAULT 0,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(list_id, channel_id)
);

-- Create user_preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_channel_lists_user_id ON channel_lists(user_id);
CREATE INDEX IF NOT EXISTS idx_channel_list_items_list_id ON channel_list_items(list_id);
CREATE INDEX IF NOT EXISTS idx_channel_list_items_channel_id ON channel_list_items(channel_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE channel_lists ENABLE ROW LEVEL SECURITY;
ALTER TABLE channel_list_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for channel_lists
CREATE POLICY "Users can view their own channel lists" ON channel_lists
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own channel lists" ON channel_lists
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own channel lists" ON channel_lists
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own channel lists" ON channel_lists
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for channel_list_items
CREATE POLICY "Users can view items in their own lists" ON channel_list_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM channel_lists 
            WHERE channel_lists.id = channel_list_items.list_id 
            AND channel_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can add items to their own lists" ON channel_list_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM channel_lists 
            WHERE channel_lists.id = channel_list_items.list_id 
            AND channel_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update items in their own lists" ON channel_list_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM channel_lists 
            WHERE channel_lists.id = channel_list_items.list_id 
            AND channel_lists.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete items from their own lists" ON channel_list_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM channel_lists 
            WHERE channel_lists.id = channel_list_items.list_id 
            AND channel_lists.user_id = auth.uid()
        )
    );

-- Create RLS policies for user_preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

-- Create functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_channel_lists_updated_at 
    BEFORE UPDATE ON channel_lists 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at 
    BEFORE UPDATE ON user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
