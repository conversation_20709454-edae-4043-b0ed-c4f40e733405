# VirSnapp - YouTube Channel Organization & Management Tool

VirSnapp is a modern web application built on React that transforms YouTube channel discovery and organization. Built upon a YouTube clone foundation, VirSnapp provides powerful tools for managing custom channel lists, discovering new content creators, and organizing your YouTube subscriptions efficiently.

## 🚀 Features

### Core Functionality
- **Channel Management System**: Add YouTube channels to custom-named lists using channel URLs
- **Channel Discovery**: Search for channels and view detailed channel information
- **List Organization**: Create, edit, delete, and organize custom channel lists
- **Channel Details**: Display comprehensive channel information including subscriber count, video count, and recent videos

### Technical Features
- **User Authentication**: Secure sign-up/sign-in with Supabase Auth
- **Real-time Data**: Live synchronization with Supabase database
- **Responsive Design**: Mobile-first design that works on all devices
- **Error Handling**: Comprehensive error boundaries and user feedback
- **URL Validation**: Smart YouTube URL parsing and validation
- **Search Functionality**: Powerful channel search with filtering

## 🛠️ Technology Stack

- **Frontend**: React 18.2.0 with functional components and hooks
- **Styling**: Tailwind CSS 3.2.4 with custom components
- **Database**: Supabase (PostgreSQL with real-time subscriptions)
- **Authentication**: Supabase Auth
- **API**: YouTube138 RapidAPI for YouTube data
- **HTTP Client**: Axios for API requests
- **Routing**: React Router DOM v6
- **Icons**: React Icons, FontAwesome
- **State Management**: React Context API

## 📋 Prerequisites

Before running VirSnapp, ensure you have:

- Node.js (v14 or higher)
- npm or yarn package manager
- A Supabase account and project
- YouTube138 RapidAPI key

## ⚙️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd virsnapp
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env` file in the root directory:

```env
# YouTube API Configuration
REACT_APP_YOUTUBE_API_KEY=your_rapidapi_key_here

# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# Application Configuration
REACT_APP_APP_NAME=VirSnapp
REACT_APP_VERSION=1.0.0
```

### 4. Database Setup
1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql` in your Supabase SQL editor
3. Enable Row Level Security (RLS) policies as defined in the schema

### 5. API Setup
1. Sign up for RapidAPI
2. Subscribe to YouTube138 API
3. Add your API key to the environment variables

## 🚀 Running the Application

### Development Mode
```bash
npm start
```
Opens [http://localhost:3000](http://localhost:3000) in your browser.

### Production Build
```bash
npm run build
```
Creates an optimized production build in the `build` folder.

## 📁 Project Structure

```
src/
├── components/           # React components
│   ├── AddChannelForm.jsx
│   ├── AuthModal.jsx
│   ├── ChannelCard.jsx
│   ├── ChannelDiscovery.jsx
│   ├── ChannelListManager.jsx
│   ├── ChannelListView.jsx
│   ├── ErrorBoundary.jsx
│   ├── Toast.jsx
│   ├── VirSnappDashboard.jsx
│   └── ...
├── context/              # React Context providers
│   ├── AuthContext.js
│   └── contextApi.js
├── lib/                  # External service integrations
│   └── supabase.js
├── utils/                # Utility functions
│   ├── api.js
│   ├── constants.js
│   └── validation.js
├── shared/               # Shared components
└── images/               # Static assets
```

## 🔐 Security Features

- **Environment Variables**: All sensitive data stored in environment variables
- **Input Validation**: Comprehensive URL and form validation
- **Row Level Security**: Database-level security with Supabase RLS
- **Error Boundaries**: Graceful error handling and user feedback
- **XSS Protection**: Input sanitization and validation

## 🎯 Usage Guide

### Getting Started
1. **Sign Up/Sign In**: Create an account or sign in to access VirSnapp features
2. **Create Lists**: Use the "New List" button to create your first channel list
3. **Add Channels**: Use the "Add Channel" button to add YouTube channels by URL
4. **Discover Channels**: Use the "Discover" tab to search for new channels
5. **Manage Lists**: Edit, delete, and organize your channel lists

### Supported YouTube URL Formats
- `https://www.youtube.com/channel/CHANNEL_ID`
- `https://www.youtube.com/c/CHANNEL_NAME`
- `https://www.youtube.com/user/USERNAME`
- `https://www.youtube.com/@HANDLE`

## 🔧 API Endpoints Used

### YouTube138 RapidAPI
- `GET /channel/details/` - Fetch channel information
- `GET /search/` - Search for channels and videos
- `GET /video/details/` - Get detailed video information

### Supabase Database Tables
- `channel_lists` - User's custom channel lists
- `channel_list_items` - Channels within lists
- `user_preferences` - User settings and preferences

## 🐛 Troubleshooting

### Common Issues

1. **API Key Errors**
   - Ensure your YouTube138 API key is valid and has sufficient quota
   - Check that the environment variable is properly set

2. **Database Connection Issues**
   - Verify Supabase URL and anon key are correct
   - Ensure RLS policies are properly configured

3. **Channel URL Validation**
   - Use supported YouTube URL formats
   - Ensure URLs are complete and valid

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Built upon the foundation of a YouTube clone project
- YouTube138 RapidAPI for YouTube data access
- Supabase for backend infrastructure
- Tailwind CSS for styling framework
- React community for excellent documentation and tools

## 📞 Support

For support, email <EMAIL> or create an issue in the GitHub repository.
