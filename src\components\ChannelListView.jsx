import React, { useState, useContext, useEffect } from 'react';
import { Context } from '../context/contextApi';
import { useAuth } from '../context/AuthContext';
import ChannelCard from './ChannelCard';
import AddChannelForm from './AddChannelForm';
import { AiOutlinePlus, AiOutlineFolder, AiOutlineSearch } from 'react-icons/ai';
import { BiLoaderAlt } from 'react-icons/bi';

const ChannelListView = ({ selectedList }) => {
    const [showAddForm, setShowAddForm] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredChannels, setFilteredChannels] = useState([]);
    
    const { loading } = useContext(Context);
    const { user } = useAuth();

    // Filter channels based on search term
    useEffect(() => {
        if (!selectedList?.channel_list_items) {
            setFilteredChannels([]);
            return;
        }

        if (!searchTerm.trim()) {
            setFilteredChannels(selectedList.channel_list_items);
        } else {
            const filtered = selectedList.channel_list_items.filter(channel =>
                channel.channel_name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredChannels(filtered);
        }
    }, [selectedList, searchTerm]);

    const handleChannelAdded = () => {
        setShowAddForm(false);
        // The context will automatically refresh the lists
    };

    const handleChannelRemoved = (removedChannel) => {
        // The context will automatically refresh the lists
        console.log('Channel removed:', removedChannel.channel_name);
    };

    if (!user) {
        return (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                <AiOutlineFolder size={64} className="mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Sign In Required</h3>
                <p className="text-center">Please sign in to view and manage your channel lists.</p>
            </div>
        );
    }

    if (!selectedList) {
        return (
            <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                <AiOutlineFolder size={64} className="mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">No List Selected</h3>
                <p className="text-center">Select a channel list from the sidebar to view its contents.</p>
            </div>
        );
    }

    return (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6">
            {/* List Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div>
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                        {selectedList.name}
                    </h2>
                    {selectedList.description && (
                        <p className="text-gray-600 dark:text-gray-400 mt-1">
                            {selectedList.description}
                        </p>
                    )}
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                        {selectedList.channel_list_items?.length || 0} channels
                    </div>
                </div>
                
                <button
                    onClick={() => setShowAddForm(true)}
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2 self-start sm:self-auto"
                >
                    <AiOutlinePlus size={16} />
                    Add Channel
                </button>
            </div>

            {/* Search Bar */}
            {selectedList.channel_list_items && selectedList.channel_list_items.length > 0 && (
                <div className="mb-6">
                    <div className="relative">
                        <AiOutlineSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                        <input
                            type="text"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            placeholder="Search channels..."
                            className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                    </div>
                </div>
            )}

            {/* Add Channel Modal/Form */}
            {showAddForm && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
                        <AddChannelForm
                            selectedListId={selectedList.id}
                            onClose={() => setShowAddForm(false)}
                            onChannelAdded={handleChannelAdded}
                        />
                    </div>
                </div>
            )}

            {/* Loading State */}
            {loading && (
                <div className="flex justify-center py-12">
                    <BiLoaderAlt className="animate-spin text-blue-600" size={32} />
                </div>
            )}

            {/* Channels List */}
            {!loading && (
                <div className="space-y-4">
                    {filteredChannels.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-12 text-gray-500 dark:text-gray-400">
                            {searchTerm ? (
                                <>
                                    <AiOutlineSearch size={48} className="mb-4 opacity-50" />
                                    <h3 className="text-lg font-medium mb-2">No Channels Found</h3>
                                    <p className="text-center">
                                        No channels match your search term "{searchTerm}".
                                    </p>
                                    <button
                                        onClick={() => setSearchTerm('')}
                                        className="mt-4 text-blue-600 dark:text-blue-400 hover:underline"
                                    >
                                        Clear search
                                    </button>
                                </>
                            ) : (
                                <>
                                    <AiOutlineFolder size={48} className="mb-4 opacity-50" />
                                    <h3 className="text-lg font-medium mb-2">No Channels Yet</h3>
                                    <p className="text-center mb-4">
                                        This list is empty. Add some YouTube channels to get started!
                                    </p>
                                    <button
                                        onClick={() => setShowAddForm(true)}
                                        className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors flex items-center gap-2"
                                    >
                                        <AiOutlinePlus size={16} />
                                        Add First Channel
                                    </button>
                                </>
                            )}
                        </div>
                    ) : (
                        <div className="grid gap-4">
                            {filteredChannels.map((channel) => (
                                <ChannelCard
                                    key={channel.id}
                                    channel={channel}
                                    onRemove={handleChannelRemoved}
                                    showRemoveButton={true}
                                />
                            ))}
                        </div>
                    )}
                </div>
            )}

            {/* Search Results Info */}
            {searchTerm && filteredChannels.length > 0 && (
                <div className="mt-4 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Showing {filteredChannels.length} of {selectedList.channel_list_items?.length || 0} channels
                </div>
            )}
        </div>
    );
};

export default ChannelListView;
