import { createClient } from '@supabase/supabase-js';

// Validate Supabase configuration
const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
    }
});

// Database helper functions
export const supabaseHelpers = {
    // Channel Lists CRUD operations
    async createChannelList(userId, name, description = '') {
        const { data, error } = await supabase
            .from('channel_lists')
            .insert([
                {
                    user_id: userId,
                    name: name,
                    description: description,
                    created_at: new Date().toISOString()
                }
            ])
            .select();
        
        if (error) throw error;
        return data[0];
    },

    async getUserChannelLists(userId) {
        const { data, error } = await supabase
            .from('channel_lists')
            .select(`
                *,
                channel_list_items (
                    id,
                    channel_url,
                    channel_name,
                    channel_id,
                    added_at
                )
            `)
            .eq('user_id', userId)
            .order('created_at', { ascending: false });
        
        if (error) throw error;
        return data;
    },

    async updateChannelList(listId, updates) {
        const { data, error } = await supabase
            .from('channel_lists')
            .update(updates)
            .eq('id', listId)
            .select();
        
        if (error) throw error;
        return data[0];
    },

    async deleteChannelList(listId) {
        const { error } = await supabase
            .from('channel_lists')
            .delete()
            .eq('id', listId);
        
        if (error) throw error;
        return true;
    },

    // Channel List Items CRUD operations
    async addChannelToList(listId, channelData) {
        const { data, error } = await supabase
            .from('channel_list_items')
            .insert([
                {
                    list_id: listId,
                    channel_url: channelData.url,
                    channel_name: channelData.name,
                    channel_id: channelData.id,
                    channel_thumbnail: channelData.thumbnail,
                    subscriber_count: channelData.subscriberCount,
                    video_count: channelData.videoCount,
                    added_at: new Date().toISOString()
                }
            ])
            .select();
        
        if (error) throw error;
        return data[0];
    },

    async removeChannelFromList(itemId) {
        const { error } = await supabase
            .from('channel_list_items')
            .delete()
            .eq('id', itemId);
        
        if (error) throw error;
        return true;
    },

    async getChannelListItems(listId) {
        const { data, error } = await supabase
            .from('channel_list_items')
            .select('*')
            .eq('list_id', listId)
            .order('added_at', { ascending: false });
        
        if (error) throw error;
        return data;
    },

    // User preferences
    async getUserPreferences(userId) {
        const { data, error } = await supabase
            .from('user_preferences')
            .select('*')
            .eq('user_id', userId)
            .single();
        
        if (error && error.code !== 'PGRST116') throw error;
        return data;
    },

    async updateUserPreferences(userId, preferences) {
        const { data, error } = await supabase
            .from('user_preferences')
            .upsert([
                {
                    user_id: userId,
                    preferences: preferences,
                    updated_at: new Date().toISOString()
                }
            ])
            .select();
        
        if (error) throw error;
        return data[0];
    }
};

// Auth helper functions
export const authHelpers = {
    async signUp(email, password) {
        const { data, error } = await supabase.auth.signUp({
            email,
            password,
        });
        
        if (error) throw error;
        return data;
    },

    async signIn(email, password) {
        const { data, error } = await supabase.auth.signInWithPassword({
            email,
            password,
        });
        
        if (error) throw error;
        return data;
    },

    async signOut() {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
    },

    async getCurrentUser() {
        const { data: { user } } = await supabase.auth.getUser();
        return user;
    },

    async resetPassword(email) {
        const { error } = await supabase.auth.resetPasswordForEmail(email);
        if (error) throw error;
    }
};
