import axios from "axios";

const BASE_URL = "https://youtube138.p.rapidapi.com";

// Validate API key exists
const API_KEY = process.env.REACT_APP_YOUTUBE_API_KEY;
if (!API_KEY) {
    throw new Error("REACT_APP_YOUTUBE_API_KEY environment variable is required");
}

const options = {
    params: { hl: "en", gl: "US" },
    headers: {
        "X-RapidAPI-Key": API_KEY,
        "X-RapidAPI-Host": "youtube138.p.rapidapi.com",
    },
};

// Enhanced API function with error handling
export const fetchDataFromApi = async (url) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/${url}`, options);
        return data;
    } catch (error) {
        console.error("API Error:", error.response?.data || error.message);
        throw new Error(
            error.response?.data?.message ||
            "Failed to fetch data from YouTube API"
        );
    }
};

// New API functions for VirSnapp functionality
export const fetchChannelDetails = async (channelUrl) => {
    try {
        // Extract channel ID or handle from URL
        const channelId = extractChannelIdFromUrl(channelUrl);
        const { data } = await axios.get(`${BASE_URL}/channel/details/`, {
            ...options,
            params: { ...options.params, id: channelId }
        });
        return data;
    } catch (error) {
        console.error("Channel Details Error:", error.response?.data || error.message);
        throw new Error("Failed to fetch channel details");
    }
};

export const searchChannels = async (query) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/search/`, {
            ...options,
            params: { ...options.params, q: query, type: "channel" }
        });
        return data;
    } catch (error) {
        console.error("Search Error:", error.response?.data || error.message);
        throw new Error("Failed to search channels");
    }
};

export const fetchVideoDetails = async (videoId) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/video/details/`, {
            ...options,
            params: { ...options.params, id: videoId }
        });
        return data;
    } catch (error) {
        console.error("Video Details Error:", error.response?.data || error.message);
        throw new Error("Failed to fetch video details");
    }
};

// Utility function to extract channel ID from various YouTube URL formats
const extractChannelIdFromUrl = (url) => {
    const patterns = [
        /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
        /youtube\.com\/@([a-zA-Z0-9_-]+)/,
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) {
            return match[1];
        }
    }

    throw new Error("Invalid YouTube channel URL format");
};
