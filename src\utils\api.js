import axios from "axios";

const BASE_URL = "https://youtube138.p.rapidapi.com";

// Validate API key exists
const API_KEY = process.env.REACT_APP_YOUTUBE_API_KEY;
if (!API_KEY) {
    throw new Error("REACT_APP_YOUTUBE_API_KEY environment variable is required");
}

const options = {
    params: { hl: "en", gl: "US" },
    headers: {
        "X-RapidAPI-Key": API_KEY,
        "X-RapidAPI-Host": "youtube138.p.rapidapi.com",
    },
};

// Enhanced API function with error handling
export const fetchDataFromApi = async (url) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/${url}`, options);
        return data;
    } catch (error) {
        console.error("API Error:", error.response?.data || error.message);
        throw new Error(
            error.response?.data?.message ||
            "Failed to fetch data from YouTube API"
        );
    }
};

// New API functions for VirSnapp functionality
export const fetchChannelDetails = async (channelUrl) => {
    console.log('fetchChannelDetails called with URL:', channelUrl);

    try {
        // Extract channel ID or handle from URL
        const channelInfo = extractChannelIdFromUrl(channelUrl);
        console.log('Extracted channel info:', channelInfo);

        // Use the channel ID for the API call
        const channelId = channelInfo.id;

        console.log('Making API call to YouTube138 with channel ID:', channelId);

        // Try different endpoint formats for YouTube138 API
        let apiUrl;
        let apiParams;

        if (channelInfo.type === 'channel') {
            // For direct channel IDs
            apiUrl = `${BASE_URL}/channel/details/`;
            apiParams = { ...options.params, id: channelId };
        } else {
            // For custom URLs, usernames, or handles - try search first
            apiUrl = `${BASE_URL}/search/`;
            apiParams = {
                ...options.params,
                q: channelId,
                type: 'channel',
                limit: 1
            };
        }

        console.log('API URL:', apiUrl);
        console.log('API Params:', apiParams);

        const { data } = await axios.get(apiUrl, {
            ...options,
            params: apiParams
        });

        console.log('YouTube138 API response:', data);
        return data;
    } catch (error) {
        console.error("Channel Details Error:", {
            message: error.message,
            response: error.response?.data,
            status: error.response?.status,
            url: channelUrl
        });

        // Provide more specific error messages
        if (error.response?.status === 401) {
            throw new Error("YouTube API key is invalid or expired");
        } else if (error.response?.status === 403) {
            throw new Error("YouTube API quota exceeded or access forbidden");
        } else if (error.response?.status === 404) {
            throw new Error("Channel not found. Please check the URL");
        } else if (error.message.includes("Invalid YouTube channel URL")) {
            throw new Error(error.message);
        } else {
            throw new Error(`Failed to fetch channel details: ${error.response?.data?.message || error.message}`);
        }
    }
};

export const searchChannels = async (query) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/search/`, {
            ...options,
            params: { ...options.params, q: query, type: "channel" }
        });
        return data;
    } catch (error) {
        console.error("Search Error:", error.response?.data || error.message);
        throw new Error("Failed to search channels");
    }
};

export const fetchVideoDetails = async (videoId) => {
    try {
        const { data } = await axios.get(`${BASE_URL}/video/details/`, {
            ...options,
            params: { ...options.params, id: videoId }
        });
        return data;
    } catch (error) {
        console.error("Video Details Error:", error.response?.data || error.message);
        throw new Error("Failed to fetch video details");
    }
};

// Utility function to extract channel ID from various YouTube URL formats
const extractChannelIdFromUrl = (url) => {
    console.log('Extracting channel ID from URL:', url);

    const patterns = [
        { regex: /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/, type: 'channel' },
        { regex: /youtube\.com\/c\/([a-zA-Z0-9_-]+)/, type: 'custom' },
        { regex: /youtube\.com\/user\/([a-zA-Z0-9_-]+)/, type: 'user' },
        { regex: /youtube\.com\/@([a-zA-Z0-9_-]+)/, type: 'handle' },
    ];

    for (const pattern of patterns) {
        const match = url.match(pattern.regex);
        if (match) {
            const result = {
                id: match[1],
                type: pattern.type,
                originalUrl: url
            };
            console.log('Successfully extracted channel info:', result);
            return result;
        }
    }

    console.error('Failed to extract channel ID from URL:', url);
    throw new Error("Invalid YouTube channel URL format. Please use a valid YouTube channel URL.");
};

// Test function to check API connectivity
export const testYouTubeAPI = async () => {
    try {
        console.log('Testing YouTube138 API connection...');
        console.log('API Key present:', !!API_KEY);
        console.log('Base URL:', BASE_URL);

        // Test with a simple search
        const { data } = await axios.get(`${BASE_URL}/search/`, {
            ...options,
            params: {
                ...options.params,
                q: 'test',
                limit: 1
            }
        });

        console.log('API Test successful:', data);
        return { success: true, data };
    } catch (error) {
        console.error('API Test failed:', {
            message: error.message,
            response: error.response?.data,
            status: error.response?.status
        });
        return {
            success: false,
            error: error.response?.data || error.message,
            status: error.response?.status
        };
    }
};
